@extends('main')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/management-style.css') }}">
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header flex items-center justify-between mb-8">
        <div class="flex items-center space-x-3">
            <div class="bg-primary-blue p-3 rounded-xl">
                <iconify-icon icon="mdi:account-details" class="text-white text-2xl"></iconify-icon>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $title }}</h1>
                <p class="text-gray-600 mt-1">{{ $exam->name }} - {{ $student->name }}</p>
            </div>
        </div>
        <a href="{{ route('exam.student', $exam->id) }}"
            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center space-x-2">
            <iconify-icon icon="mdi:arrow-left"></iconify-icon>
            <span>Kembali</span>
        </a>
    </div>

    <!-- Student Info Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="text-sm font-medium text-gray-500">Nama Siswa</label>
                <p class="text-gray-900 font-medium">{{ $student->name }}</p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">NISN</label>
                <p class="text-gray-900">{{ $student->nisn }}</p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Kelas</label>
                <p class="text-gray-900">{{ $exam->class->name ?? '-' }}</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div>
                <label class="text-sm font-medium text-gray-500">Skor</label>
                <p class="text-2xl font-bold text-blue-600" id="total-score">{{ $examResult->score }}</p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Status</label>
                <span
                    class="inline-flex px-2 py-1 text-xs font-medium rounded-full
                        {{ $examResult->status === 'graded' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                    {{ $examResult->status === 'graded' ? 'Sudah Dikoreksi' : 'Belum Dikoreksi' }}
                </span>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Percobaan</label>
                <p class="text-gray-900">{{ $examResult->attempt }}</p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Waktu Submit</label>
                <p class="text-gray-900">{{ \Carbon\Carbon::parse($examResult->submitted_at)->format('d/m/Y H:i') }}
                </p>
            </div>
        </div>
    </div>

    <!-- Questions and Answers -->
    @foreach ($questionsByType as $type => $questions)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">
                    @if ($type === 'pilihan_ganda')
                        Soal Pilihan Ganda
                    @elseif($type === 'uraian_singkat')
                        Soal Uraian Singkat
                    @elseif($type === 'esai')
                        Soal Esai
                    @endif
                    <span class="text-sm font-normal text-gray-500">({{ $questions->count() }} soal)</span>
                </h2>
            </div>

            <div class="p-6">
                @foreach ($questions as $index => $question)
                    @php
                        $studentAnswer = collect($studentAnswers)->firstWhere('question_id', $question->id);
                    @endphp

                    <div class="mb-8 pb-6 {{ !$loop->last ? 'border-b border-gray-100' : '' }}">
                        <!-- Question -->
                        <div class="mb-4">
                            <h3 class="font-medium text-gray-900 mb-2">
                                {{ $loop->iteration }}. {{ $question->question }}
                            </h3>

                            @if ($question->img)
                                @php
                                    $images = explode(',', $question->img);
                                @endphp
                                <div class="mb-3">
                                    @foreach ($images as $image)
                                        @if (trim($image))
                                            <img src="{{ asset('storage/uploads/images/question/' . trim($image)) }}"
                                                alt="Question Image"
                                                class="max-w-md rounded-lg border border-gray-200 mb-2 block">
                                        @endif
                                    @endforeach
                                </div>
                            @endif
                        </div>

                        <!-- Answer Section -->
                        @if ($type === 'pilihan_ganda')
                            <div class="space-y-2">
                                @foreach ($question->answers as $answer)
                                    @php
                                        $isSelected =
                                            $studentAnswer && $studentAnswer['selected_answer'] == $answer->id;
                                        $isCorrect = $answer->is_correct;
                                    @endphp

                                    <div
                                        class="flex items-center p-3 rounded-lg border
                                            {{ $isSelected && $isCorrect
                                                ? 'bg-green-50 border-green-200'
                                                : ($isSelected && !$isCorrect
                                                    ? 'bg-red-50 border-red-200'
                                                    : ($isCorrect
                                                        ? 'bg-blue-50 border-blue-200'
                                                        : 'bg-gray-50 border-gray-200')) }}">

                                        <div class="flex items-center mr-3">
                                            @if ($isSelected)
                                                <iconify-icon
                                                    icon="{{ $isCorrect ? 'mdi:check-circle' : 'mdi:close-circle' }}"
                                                    class="text-lg {{ $isCorrect ? 'text-green-600' : 'text-red-600' }}"></iconify-icon>
                                            @elseif($isCorrect)
                                                <iconify-icon icon="mdi:check-circle-outline"
                                                    class="text-lg text-blue-600"></iconify-icon>
                                            @else
                                                <span class="w-5 h-5 rounded-full border-2 border-gray-300"></span>
                                            @endif
                                        </div>

                                        <span class="text-gray-900">{{ $answer->answer }}</span>

                                        @if ($isSelected)
                                            <span
                                                class="ml-auto text-xs font-medium
                                                    {{ $isCorrect ? 'text-green-600' : 'text-red-600' }}">
                                                {{ $isCorrect ? 'Jawaban Siswa (Benar)' : 'Jawaban Siswa (Salah)' }}
                                            </span>
                                        @elseif($isCorrect)
                                            <span class="ml-auto text-xs font-medium text-blue-600">
                                                Jawaban Benar
                                            </span>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @elseif($type === 'uraian_singkat')
                            @php
                                $correctAnswer = $question->answers->where('is_correct', 1)->first();
                                $studentAnswerText = $studentAnswer['text_answer'] ?? '';
                                $isCorrect = false;

                                if ($correctAnswer && !empty($studentAnswerText)) {
                                    $studentAnswerNormalized = trim(strtolower($studentAnswerText));
                                    $correctAnswerNormalized = trim(strtolower($correctAnswer->answer));
                                    $isCorrect = $studentAnswerNormalized === $correctAnswerNormalized;
                                }
                            @endphp

                            <div class="space-y-4">
                                <!-- Correct Answer -->
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <label class="text-sm font-medium text-blue-700 mb-2 block">Jawaban yang Benar:</label>
                                    <p class="text-blue-900">{{ $correctAnswer->answer ?? 'Tidak ada jawaban yang benar' }}
                                    </p>
                                </div>

                                <!-- Student Answer -->
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <label class="text-sm font-medium text-gray-700 mb-2 block">Jawaban Siswa:</label>
                                    <div class="flex items-start justify-between">
                                        <p class="text-gray-900 flex-1">{{ $studentAnswerText ?: 'Tidak dijawab' }}</p>
                                        @if (!empty($studentAnswerText))
                                            <span
                                                class="ml-4 text-xs font-medium px-2 py-1 rounded-full {{ $isCorrect ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700' }}">
                                                {{ $isCorrect ? 'Benar' : 'Salah' }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @elseif($type === 'esai')
                            <div class="space-y-4">
                                <!-- Student Answer -->
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <label class="text-sm font-medium text-gray-700 mb-2 block">Jawaban Siswa:</label>
                                    <p class="text-gray-900">
                                        {{ $studentAnswer['text_answer'] ?? 'Tidak dijawab' }}</p>
                                    @if (isset($studentAnswer['image_path']) && !empty($studentAnswer['image_path']))
                                        <div class="mt-3">
                                            <label class="text-sm font-medium text-gray-700 mb-2 block">Gambar
                                                Jawaban:</label>
                                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                                @php
                                                    // Check if image_path is a filename or full path
                                                    $imagePath = $studentAnswer['image_path'];
                                                    if (strpos($imagePath, '/') === false) {
                                                        // It's just a filename, use answers directory
    $imageUrl = asset(
        'storage/uploads/images/answers/' . $imagePath,
    );
} else {
    // It's a full path (legacy), try to extract filename or show placeholder
                                                        $imageUrl = asset(
                                                            'storage/uploads/images/answers/placeholder.jpg',
                                                        );
                                                    }
                                                @endphp
                                                <img src="{{ $imageUrl }}" alt="Answer Image"
                                                    class="w-full h-32 object-cover rounded-lg border border-gray-200"
                                                    onerror="this.src='{{ asset('storage/uploads/images/placeholder.jpg') }}'; this.onerror=null;">
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Essay Scoring -->
                                @php
                                    $essayAnswer = $question->answers->where('is_correct', 1)->first();
                                    $maxScore = $essayAnswer->score ?? 100;
                                    $currentScore = $studentAnswer['essay_score'] ?? 0;
                                @endphp
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <label class="text-sm font-medium text-gray-700 mb-2 block">Penilaian Guru:</label>
                                    <div class="flex items-center space-x-3">
                                        <input type="number" id="essay-score-{{ $question->id }}"
                                            value="{{ $currentScore }}" min="0" max="{{ $maxScore }}"
                                            data-max-score="{{ $maxScore }}"
                                            class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <span class="text-gray-600">/ {{ $maxScore }}</span>
                                        <button onclick="updateEssayScore({{ $question->id }})"
                                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                                            Simpan Nilai
                                        </button>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2">Skor maksimal: {{ $maxScore }} poin</p>
                                </div>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    @endforeach
    </div>

    <script>
        function updateEssayScore(questionId) {
            const scoreInput = document.getElementById(`essay-score-${questionId}`);
            const score = parseFloat(scoreInput.value) || 0;
            const maxScore = parseFloat(scoreInput.getAttribute('data-max-score')) || 100;

            if (score < 0 || score > maxScore) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: `Nilai harus antara 0 dan ${maxScore}`,
                });
                return;
            }

            // Show loading
            Swal.fire({
                title: 'Menyimpan...',
                text: 'Sedang menyimpan nilai',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/exam/{{ $exam->id }}/student/{{ $student->id }}/essay-score`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        question_id: questionId,
                        score: score
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update total score display
                        document.getElementById('total-score').textContent = data.new_total_score;

                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: data.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: data.message,
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menyimpan nilai',
                    });
                });
        }
    </script>
@endsection
