@extends('main')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/management-style.css') }}">
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header flex items-center justify-between mb-8">
        <div class="flex items-center space-x-3">
            <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h1 class="page-title text-3xl font-bold text-gray-800"><PERSON><PERSON><PERSON><PERSON></h1>
        </div>

        <div class="page-header-actions flex items-center space-x-4">
            <!-- Search Bar -->
            <div class="search-container relative">
                <input type="text" id="searchInput" placeholder="Cari kelas di sini..."
                    class="search-input w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent">
                <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>

            <!-- Add Button -->
            <button onclick="openCreateModal()"
                class="btn-primary bg-primary-blue text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 hover:shadow-lg hover:scale-105 transition-all duration-200 ease-in-out">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span>Tambah Kelas Baru</span>
            </button>
        </div>
    </div>

    <!-- Cards Grid -->
    <div class="cards-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="cardsContainer">
        @forelse ($StudentClass as $class)
            <div class="card-hover bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow class-card"
                data-name="{{ strtolower($class->name) }}">

                <!-- Card Header -->
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 line-clamp-2">{{ $class->name }}</h3>

                    <!-- Dropdown Menu -->
                    <div class="relative">
                        <button
                            class="dropdown-toggle text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
                            data-dropdown="dropdown-{{ $class->id }}">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                                </path>
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="dropdown-{{ $class->id }}"
                            class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10 hidden">
                            <div class="py-1">
                                <a href="{{ route('class.edit', $class->id) }}"
                                    onclick="openEditModal(event, this.href); return false;"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                    <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                    Edit Kelas
                                </a>
                                <button onclick="deleteClass({{ $class->id }}, '{{ $class->name }}')"
                                    class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                    Hapus Kelas
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card Content -->
                <div class="space-y-3">
                    <!-- Class Info -->
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                        <span>Kelas {{ $class->name }}</span>
                    </div>

                    <!-- Student Count (if available) -->
                    @if (isset($class->students_count))
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            <span>{{ $class->students_count ?? 0 }} Siswa</span>
                        </div>
                    @endif

                    <!-- Created Date -->
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        <span>Dibuat {{ $class->created_at ? $class->created_at->format('d M Y') : '-' }}</span>
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-500">ID: {{ $class->id }}</span>
                        <div class="flex space-x-2">
                            <a href="{{ route('class.edit', $class->id) }}"
                                onclick="openEditModal(event, this.href); return false;"
                                class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors">
                                Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <!-- Empty State -->
            <div class="col-span-full flex flex-col items-center justify-center py-12">
                <svg class="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                    </path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada kelas</h3>
                <p class="text-gray-500 mb-6">Mulai dengan membuat kelas pertama Anda</p>
                <button onclick="openCreateModal()"
                    class="bg-primary-blue text-white px-6 py-3 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 transition-all">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Tambah Kelas Baru</span>
                </button>
            </div>
        @endforelse
    </div>

    <!-- Hidden form for delete -->
    <form id="deleteForm" method="POST" style="display: none;">
        @csrf
        @method('DELETE')
    </form>
@endsection

@section('script')
    <script src="{{ asset('package/dist/libs/sweetalert2/dist/sweetalert2.min.js') }}"></script>
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.class-card');

            cards.forEach(card => {
                const name = card.getAttribute('data-name');
                if (name.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // Dropdown functionality
        document.addEventListener('click', function(e) {
            // Close all dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });

            // Open clicked dropdown
            if (e.target.closest('.dropdown-toggle')) {
                e.preventDefault();
                const button = e.target.closest('.dropdown-toggle');
                const dropdownId = button.getAttribute('data-dropdown');
                const dropdown = document.getElementById(dropdownId);
                dropdown.classList.toggle('hidden');
            }
        });

        // Modal functions
        function openCreateModal() {
            console.log('Opening create modal');

            fetch("{{ route('class.add') }}")
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    console.log('Modal content loaded successfully');
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    if (!modal || !content) {
                        console.error('Modal elements not found');
                        return;
                    }

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animation
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal membuka form tambah kelas. Silakan coba lagi.',
                        confirmButtonColor: '#455A9D'
                    });
                });
        }

        function openEditModal(event, url) {
            event.preventDefault();
            event.stopPropagation();

            console.log('Opening edit modal for URL:', url);

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    console.log('Modal content loaded successfully');
                    const modal = document.getElementById('modalGlobal');
                    const content = document.getElementById('modalContent');

                    if (!modal || !content) {
                        console.error('Modal elements not found');
                        return;
                    }

                    content.innerHTML = html;
                    modal.classList.remove('hidden');

                    // Animation
                    setTimeout(() => {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }, 50);

                    setupModalListeners();
                })
                .catch(error => {
                    console.error('Gagal membuka modal edit:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal membuka form edit. Silakan coba lagi.',
                        confirmButtonColor: '#455A9D'
                    });
                });
        }

        function setupModalListeners() {
            // Focus on first input
            const firstInput = document.querySelector('#modalContent input[type="text"]');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }

            // Form validation
            const form = document.querySelector('#modalContent form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const nameField = form.querySelector('input[name="name"]');

                    if (nameField && nameField.value.trim() === '') {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Nama kelas harus diisi!',
                            confirmButtonColor: '#455A9D'
                        });
                        nameField.focus();
                        return;
                    }
                });
            }
        }

        function closeModal() {
            const modal = document.getElementById('modalGlobal');
            const content = document.getElementById('modalContent');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                content.innerHTML = '';
            }, 200);
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('modalGlobal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('modalGlobal');
                if (!modal.classList.contains('hidden')) {
                    closeModal();
                }
            }
        });

        // Delete function
        function deleteClass(id, name) {
            Swal.fire({
                title: 'Apakah Anda yakin?',
                text: `Kelas "${name}" akan dihapus permanen!`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.getElementById('deleteForm');
                    form.action = `/management/class/delete/${id}`;
                    form.submit();
                }
            });
        }

        // Show success/error messages
        @if (session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: '{{ session('success') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if (session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: '{{ session('error') }}',
                timer: 3000,
                showConfirmButton: false
            });
        @endif

        @if ($errors->any())
            let errorMessages = '';
            @foreach ($errors->all() as $error)
                errorMessages += '• {!! addslashes(str_replace(["\r\n", "\r", "\n"], "\\n", $error)) !!}\n';
            @endforeach

            Swal.fire({
                icon: 'error',
                title: 'Validasi Gagal!',
                html: '<div style="text-align: left; white-space: pre-line;">' + errorMessages + '</div>',
                timer: 8000,
                showConfirmButton: true,
                confirmButtonText: 'OK'
            });
        @endif
    </script>
@endsection
