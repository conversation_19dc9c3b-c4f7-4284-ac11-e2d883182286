import 'package:flutter/material.dart';
import 'buat_soal_pilihan_ganda_page.dart';
import 'buat_soal_uraian_singkat_page.dart';
import 'buat_soal_esai_page.dart';
import 'models/question_material.dart';

class BuatSoalBaruPage extends StatefulWidget {
  final QuestionMaterial material;

  const BuatSoalBaruPage({super.key, required this.material});

  @override
  State<BuatSoalBaruPage> createState() => _BuatSoalBaruPageState();
}

class _BuatSoalBaruPageState extends State<BuatSoalBaruPage> {
  int selectedQuestionType = 1; // Default to "Pilihan Ganda"
  bool _hasCreatedQuestion = false; // Track if any question was created

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
          onPressed: () => Navigator.pop(context, _hasCreatedQuestion),
        ),
        title: const Text(
          'Buat Soal Baru',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        ),
        centerTitle: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                image: const DecorationImage(
                  image: AssetImage('assets/image-bg.jpg'),
                  fit: BoxFit.cover,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: const BoxDecoration(
                      color: Color(0xFF455A9D),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.help_outline,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pilih Tipe Pertanyaan',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF333333),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Tentukan format soal yang ingin kamu buat.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Progress Bar
            Container(
              height: 6,
              decoration: BoxDecoration(
                color: const Color(0xFFE9ECEF),
                borderRadius: BorderRadius.circular(3),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: 0.5, // 1/2 progress
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF455A9D),
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 32),

            // Question Type Options
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // Pilihan Ganda
                    _buildQuestionTypeCard(
                      type: 1,
                      icon: Icons.radio_button_checked,
                      iconColor: const Color(0xFF28A745),
                      title: 'Pilihan Ganda',
                      description:
                          'Pertanyaan dengan beberapa opsi jawaban. Siswa memilih satu jawaban yang paling tepat.',
                    ),
                    const SizedBox(height: 16),

                    // Uraian Singkat
                    _buildQuestionTypeCard(
                      type: 2,
                      icon: Icons.short_text,
                      iconColor: const Color(0xFF007BFF),
                      title: 'Uraian Singkat',
                      description:
                          'Pertanyaan yang membutuhkan jawaban singkat dan ringkas, biasanya dalam satu kata.',
                    ),
                    const SizedBox(height: 16),

                    // Esai
                    _buildQuestionTypeCard(
                      type: 3,
                      icon: Icons.article_outlined,
                      iconColor: const Color(0xFF6C757D),
                      title: 'Esai',
                      description:
                          'Pertanyaan yang membutuhkan jawaban yang lebih panjang dan rinci.',
                    ),
                    const SizedBox(height: 20), // Add some bottom padding for better scrolling
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: const BorderSide(color: Color(0xFF455A9D)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Kembali',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF455A9D),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: () async {
                    // Navigate to appropriate question creation page
                    dynamic result;

                    if (selectedQuestionType == 1) {
                      // Pilihan Ganda
                      result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => BuatSoalPilihanGandaPage(
                                material: widget.material,
                              ),
                        ),
                      );
                    } else if (selectedQuestionType == 2) {
                      // Uraian Singkat
                      result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => BuatSoalUraianSingkatPage(
                                material: widget.material,
                              ),
                        ),
                      );
                    } else if (selectedQuestionType == 3) {
                      // Esai
                      result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  BuatSoalEsaiPage(material: widget.material),
                        ),
                      );
                    } else {
                      // For other types, show coming soon message
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            '${_getSelectedQuestionTypeName()} akan segera tersedia!',
                          ),
                          backgroundColor: const Color(0xFF455A9D),
                        ),
                      );
                      return;
                    }

                    // If a question was successfully created, go back and trigger refresh
                    if (result == true) {
                      setState(() {
                        _hasCreatedQuestion = true;
                      });

                      if (mounted && context.mounted) {
                        Navigator.pop(context, true);
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF455A9D),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Lanjut Buat Soal',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionTypeCard({
    required int type,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String description,
  }) {
    final bool isSelected = selectedQuestionType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedQuestionType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF8F9FA) : Colors.white,
          border: Border.all(
            color:
                isSelected ? const Color(0xFF455A9D) : const Color(0xFFE9ECEF),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // Selection Radio
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected
                          ? const Color(0xFF455A9D)
                          : const Color(0xFFCED4DA),
                  width: 2,
                ),
                color:
                    isSelected ? const Color(0xFF455A9D) : Colors.transparent,
              ),
              child:
                  isSelected
                      ? const Icon(Icons.circle, size: 8, color: Colors.white)
                      : null,
            ),
            const SizedBox(width: 16),

            // Icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: iconColor, size: 20),
            ),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color:
                          isSelected
                              ? const Color(0xFF455A9D)
                              : const Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF666666),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSelectedQuestionTypeName() {
    switch (selectedQuestionType) {
      case 1:
        return 'Pilihan Ganda dipilih';
      case 2:
        return 'Uraian Singkat dipilih';
      case 3:
        return 'Esai dipilih';
      default:
        return 'Tipe soal dipilih';
    }
  }
}
